package com.sodexo.web.controller.part;

import com.sodexo.common.annotation.Log;
import com.sodexo.common.constant.CacheConstants;
import com.sodexo.common.core.controller.BaseController;
import com.sodexo.common.core.domain.AjaxResult;
import com.sodexo.common.core.domain.entity.SysUser;
import com.sodexo.common.core.page.TableDataInfo;
import com.sodexo.common.enums.BusinessType;
import com.sodexo.common.utils.poi.ExcelUtil;
import com.sodexo.system.domain.SupplyRisk;
import com.sodexo.system.domain.vo.SupplyRiskVo;
import com.sodexo.system.service.ISupplyRiskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@RestController
@RequestMapping("/part/supplyRisk")
public class SupplyRiskController extends BaseController {
    @Autowired
    private ISupplyRiskService  supplyRiskService;

    private static  final String KEY="supply_risk";

    //获取结果集

    @GetMapping("/list")
    @Cacheable(value = CacheConstants.SUPPLY_RISK+KEY)
    public TableDataInfo list(SupplyRisk supplyRisk)
    {
        startPage();
        List<SupplyRiskVo> list = supplyRiskService.selectSupplyRiskList(supplyRisk);
        return getDataTable(list);
    }
    //修改
    @PreAuthorize("@ss.hasPermi('part:plantView:edit')")
    @Log(title = "Plant View", businessType = BusinessType.UPDATE)
    @PutMapping
    @CacheEvict(value = CacheConstants.SUPPLY_RISK + "*", allEntries = true)
    public AjaxResult edit(@Validated @RequestBody SupplyRiskVo supplyRiskVo)
    {
        return success(supplyRiskService.upd(supplyRiskVo));
    }

    //查询详情

    @GetMapping(value = "/{index}")
    public AjaxResult getInfo(@PathVariable Long index)
    {
        return success(supplyRiskService.selectSupplyRiskByIndex(index));
    }

    //获取饼图
    @GetMapping(value = "/scale")
    @Cacheable(value = CacheConstants.SUPPLY_SCALE+KEY)
    public AjaxResult scale(SupplyRisk supplyRisk)
    {
        List<Map<String, Object>> scale = supplyRiskService.scale(supplyRisk);
        return success(scale);
    }

    //HM数据
    @GetMapping(value = "/getMMaterialNo")
    @Cacheable(value = CacheConstants.SUPPLY_RISK+KEY)
    public AjaxResult getMMaterialNo(SupplyRisk supplyRisk)
    {
        List<SupplyRisk> mList = supplyRiskService.getMList(supplyRisk);
        return success(mList);
    }

    //获取折线图
    @GetMapping(value = "/dataChart")
    @Cacheable(value = CacheConstants.SUPPLY_CHART+KEY)
    public AjaxResult dataChart(SupplyRisk supplyRisk)
    {
        return success(supplyRiskService.dataChart(supplyRisk));
    }

    //PlantViewExport
    @Log(title = "SupplyRisk导出", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermi('part:plantView:export')")
    public void export(HttpServletResponse response, SupplyRisk supplyRisk)
    {
        List<SupplyRiskVo> list = supplyRiskService.selectSupplyRiskList(supplyRisk);
        ExcelUtil<SupplyRiskVo> util = new ExcelUtil<SupplyRiskVo>(SupplyRiskVo.class);
        util.exportExcel(response, list, "SupplyRisk");
    }

    //exportDashboard
    @Log(title = "SupplyRisk导出", businessType = BusinessType.EXPORT)
    @PostMapping("/exportDashboard")
    @PreAuthorize("@ss.hasPermi('part:Dashboard:export')")
    public void exportData(HttpServletResponse response, SupplyRisk supplyRisk)
    {
        List<SupplyRiskVo> list = supplyRiskService.exportDashboard(supplyRisk);
        ExcelUtil<SupplyRiskVo> util = new ExcelUtil<SupplyRiskVo>(SupplyRiskVo.class);
        util.exportExcel(response, list, "SupplyRisk");
    }
    //下载模板
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<SupplyRiskVo> util = new ExcelUtil<SupplyRiskVo>(SupplyRiskVo.class);
        util.importTemplateExcel(response,  "SupplyRisk");
    }

    //导入
    @Log(title = "SupplyRisk导入", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('part:plantView:import')")
    @PostMapping("/importData")
    @CacheEvict(value = CacheConstants.SUPPLY_RISK + "*", allEntries = true)
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SupplyRiskVo> util = new ExcelUtil<SupplyRiskVo>(SupplyRiskVo.class);
        List<SupplyRiskVo>  supplyRiskList = util.importExcel(file.getInputStream());
        String message = supplyRiskService.importSupplyRisk(supplyRiskList, updateSupport);
        return success(message);
    }

    //获取查询条件supplier
    @GetMapping("/getSupplierList")
    @Cacheable(value = CacheConstants.SUPPLY_RISK+KEY,key = "'supplierList'")
    public AjaxResult getSupplierList()
    {
        List<String> list = supplyRiskService.getSupplierList();
        return success(list);
    }

}
