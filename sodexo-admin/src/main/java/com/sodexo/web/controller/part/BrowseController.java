package com.sodexo.web.controller.part;

import com.sodexo.common.annotation.Log;
import com.sodexo.common.core.controller.BaseController;
import com.sodexo.common.core.domain.AjaxResult;
import com.sodexo.common.core.domain.entity.SysUser;
import com.sodexo.common.core.page.TableDataInfo;
import com.sodexo.common.enums.BusinessType;
import com.sodexo.common.utils.poi.ExcelUtil;
import com.sodexo.system.domain.Browse;
import com.sodexo.system.domain.Critertion;
import com.sodexo.system.domain.SupplyRisk;
import com.sodexo.system.domain.VendorList;
import com.sodexo.system.domain.vo.BrowseVo;
import com.sodexo.system.service.IBrowseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
@RestController
@RequestMapping("/part/browse")
public class BrowseController extends BaseController {
    @Autowired
    private IBrowseService browseService;

    @GetMapping(value = "/dataChart")
    public AjaxResult dataChart(Browse browse)
    {
        return success(browseService.dataChart(browse));
    }

    @GetMapping(value = "/workData")
    public AjaxResult workData(Browse browse)
    {
        return success(browseService.workData(browse));
    }


    //添加
    @PostMapping
    public AjaxResult add(@RequestBody @Validated Browse browse){
        return  toAjax(browseService.add(browse));
    }

    //导出
    @Log(title = "操作统计", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('part:browse:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, Browse browse)
    {
        List<Browse> list = browseService.export(browse);
        ExcelUtil<Browse> util = new ExcelUtil<Browse>(Browse.class);
        util.exportExcel(response, list, "操作统计");
    }
}
