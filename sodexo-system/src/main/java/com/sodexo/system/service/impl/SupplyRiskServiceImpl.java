package com.sodexo.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sodexo.common.exception.ServiceException;
import com.sodexo.common.utils.StringUtils;
import com.sodexo.system.domain.PlantRisk;
import com.sodexo.system.domain.SupplyRisk;
import com.sodexo.system.domain.vo.SupplyRiskVo;
import com.sodexo.system.mapper.PlantRiskMapper;
import com.sodexo.system.mapper.SupplyRiskMapper;
import com.sodexo.system.service.IPlantRiskService;
import com.sodexo.system.service.ISupplyRiskService;
import com.sun.scenario.effect.impl.sw.sse.SSEBlend_SRC_OUTPeer;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Service
public class SupplyRiskServiceImpl extends ServiceImpl<SupplyRiskMapper, SupplyRisk> implements ISupplyRiskService {
    @Resource
    private SupplyRiskMapper supplyRiskMapper;
    @Resource
    private PlantRiskMapper plantRiskMapper;
    @Resource
    private IPlantRiskService plantRiskService;

    @Override
    public List<SupplyRiskVo> selectSupplyRiskList(SupplyRisk supplyRisk) {
        return supplyRiskMapper.selectSupplyRiskList(supplyRisk);
    }

    @Override
    public int upd(SupplyRiskVo supplyRiskVo) {

        //创建对象
        SupplyRisk supplyRisk = new SupplyRisk();
        PlantRisk plantRisk = new PlantRisk();
        //赋值
        plantRisk.setPlant(supplyRiskVo.getPlant());
        plantRisk.setMaterialNo(supplyRiskVo.getMaterialNo());
        plantRisk.setPlantRiskInputs(supplyRiskVo.getPlantRiskInputs());
        plantRisk.setLocalizationMassProductionDate(supplyRiskVo.getLocalizationMassProductionDate());
        BeanUtils.copyProperties(supplyRiskVo, supplyRisk, SupplyRisk.class);
        if(StringUtils.isNotEmpty(supplyRiskVo.getPlantRiskInputs())){
            //替换空格
            String afterValue = supplyRiskVo.getPlantRiskInputs().replaceAll("\\s+", "");
            //判断是否包含
            if (Pattern.compile(Pattern.quote("lowrisk"), Pattern.CASE_INSENSITIVE).matcher(afterValue).find()) {
                supplyRisk.setRisk("L");
            }
            if (Pattern.compile(Pattern.quote("highrisk"), Pattern.CASE_INSENSITIVE).matcher(afterValue).find()) {
                supplyRisk.setRisk("H");
            }
            if (Pattern.compile(Pattern.quote("mediumrisk"), Pattern.CASE_INSENSITIVE).matcher(afterValue).find()) {
                supplyRisk.setRisk("M");
            }
        }
        QueryWrapper<PlantRisk> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("plant", plantRisk.getPlant());
        queryWrapper.eq("material_no", plantRisk.getMaterialNo());
        PlantRisk onePlantRisk = plantRiskMapper.selectOne(queryWrapper);
        if (StringUtils.isNotNull(onePlantRisk)) {
            plantRiskMapper.update(plantRisk, queryWrapper);
        } else {
            plantRiskMapper.insert(plantRisk);
        }

        return supplyRiskMapper.updateById(supplyRisk);
    }

    @Override
    public SupplyRiskVo selectSupplyRiskByIndex(Long index) {
        return supplyRiskMapper.selectSupplyRiskVoById(index);
    }

    //饼图
    @Override
    public List<Map<String, Object>> scale(SupplyRisk supplyRisk) {
        QueryWrapper<SupplyRisk> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("risk as  name", "count(risk) as value");
        if (StringUtils.isNotEmpty(supplyRisk.getPlantArray())) {
            queryWrapper.in("plant", Arrays.asList(supplyRisk.getPlantArray()));
        }

        if (StringUtils.isNotEmpty(supplyRisk.getSupplierArray())) {
            queryWrapper.in("tier_1_vendor_name", Arrays.asList(supplyRisk.getSupplierArray()));
        }
        if ("HD".equals(supplyRisk.getProductCategoryPl()) ) {
            queryWrapper.likeRight("product_category_pl", "HDPNL");
        }
        if (StringUtils.isNotEmpty(supplyRisk.getProductCategoryPl()) && !"HD".equals(supplyRisk.getProductCategoryPl()) ) {
            queryWrapper.likeLeft("product_category_pl", supplyRisk.getProductCategoryPl());
        }
        if (StringUtils.isNotEmpty(supplyRisk.getRiskArray())) {
            queryWrapper.in("risk", Arrays.asList(supplyRisk.getRiskArray()));
        }
        if (StringUtils.isNotEmpty(supplyRisk.getShippingModeArray())) {
            queryWrapper.in("shipping_mode", Arrays.asList(supplyRisk.getShippingModeArray()));
        }
        if(StringUtils.isNotEmpty(supplyRisk.getImportDomestic())){
            queryWrapper.eq("import_domestic",supplyRisk.getImportDomestic());
        }
        //判断是否过滤MTS
        if ("true".equals(supplyRisk.getStockingPolicy())) {
            queryWrapper.eq("stocking_policy", "MTS");
        }
        if ("true".equals(supplyRisk.getOnBoardSsp())) {
            queryWrapper.eq("on_board_ssp", "Y");
        }else{
            queryWrapper.eq("on_board_ssp", "N");
        }
        //屏蔽SSVF
        queryWrapper.notLike("plant", "%SSVF%");

        queryWrapper.groupBy("risk");
        List<Map<String, Object>> maps = supplyRiskMapper.selectMaps(queryWrapper);
        return maps;
    }

    //折线图
    @Override
    public List<SupplyRisk> dataChart(SupplyRisk supplyRisk) {
        QueryWrapper<SupplyRisk> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                select("material_no,soh_intransit_coverage_wkd,wk_1_soh_intransit_coverage_wkd,wk_2_soh_intransit_coverage_wkd," +
                        "wk_3_soh_intransit_coverage_wkd,wk_4_soh_intransit_coverage_wkd,wk_5_soh_intransit_coverage_wkd," +
                        "wk_6_soh_intransit_coverage_wkd,wk_7_soh_intransit_coverage_wkd,wk_8_soh_intransit_coverage_wkd," +
                        "wk_9_soh_intransit_coverage_wkd,wk_10_soh_intransit_coverage_wkd");
        if (StringUtils.isNotEmpty(supplyRisk.getPlantArray())) {
            queryWrapper.in("plant", Arrays.asList(supplyRisk.getPlantArray()));
        }
        if (StringUtils.isNotEmpty(supplyRisk.getSupplierArray())) {
            queryWrapper.in("tier_1_vendor_name", Arrays.asList(supplyRisk.getSupplierArray()));
        }

        if (StringUtils.isNotEmpty(supplyRisk.getProductCategoryPlArray()) ) {
            queryWrapper.in("product_category_pl", Arrays.asList(supplyRisk.getProductCategoryPlArray()));
        }

        if (StringUtils.isNotEmpty(supplyRisk.getRiskArray())) {
            queryWrapper.in("risk", Arrays.asList(supplyRisk.getRiskArray()));
        }
        if (StringUtils.isNotEmpty(supplyRisk.getShippingModeArray())) {
            queryWrapper.in("shipping_mode", Arrays.asList(supplyRisk.getShippingModeArray()));
        }
        if (StringUtils.isNotEmpty(supplyRisk.getMaterialNo())) {
            queryWrapper.eq("material_no", supplyRisk.getMaterialNo());
        }
        if ("true".equals(supplyRisk.getStockingPolicy())) {
            queryWrapper.eq("stocking_policy", "MTS");
        }
        if ("true".equals(supplyRisk.getOnBoardSsp())) {
            queryWrapper.eq("on_board_ssp", "Y");
        }else{
            queryWrapper.eq("on_board_ssp", "N");
        }
        if(StringUtils.isNotEmpty(supplyRisk.getImportDomestic())){
            queryWrapper.eq("import_domestic",supplyRisk.getImportDomestic());
        }
        queryWrapper.notLike("plant", "%SSVF%");
        List<SupplyRisk> list = supplyRiskMapper.selectList(queryWrapper);
        return list;
    }


    //HM数据
    @Override
    public List<SupplyRisk> getMList(SupplyRisk supplyRisk) {
        QueryWrapper<SupplyRisk> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(supplyRisk.getPlantArray())) {
            queryWrapper.in("plant", Arrays.asList(supplyRisk.getPlantArray()));
        }
        if ("HD".equals(supplyRisk.getProductCategoryPl()) ) {
            queryWrapper.likeRight("product_category_pl", "HDPNL");
        }
        if (StringUtils.isNotEmpty(supplyRisk.getProductCategoryPl()) && !"HD".equals(supplyRisk.getProductCategoryPl()) ) {
            queryWrapper.likeLeft("product_category_pl", supplyRisk.getProductCategoryPl());
        }
        if (StringUtils.isNotEmpty(supplyRisk.getRisk())) {
            queryWrapper.eq("risk", supplyRisk.getRisk());
        }
        if (StringUtils.isNotEmpty(supplyRisk.getRiskArray())) {
            queryWrapper.in("risk", Arrays.asList(supplyRisk.getRiskArray()));
        }
        if (StringUtils.isNotEmpty(supplyRisk.getSupplierArray())) {
            queryWrapper.in("tier_1_vendor_name", Arrays.asList(supplyRisk.getSupplierArray()));
        }
        if (StringUtils.isNotEmpty(supplyRisk.getShippingModeArray())) {
            queryWrapper.in("shipping_mode", Arrays.asList(supplyRisk.getShippingModeArray()));
        }
        if ("true".equals(supplyRisk.getStockingPolicy())) {
            queryWrapper.eq("stocking_policy", "MTS");
        }
        if ("true".equals(supplyRisk.getOnBoardSsp())) {
            queryWrapper.eq("on_board_ssp", "Y");
        }else{
            queryWrapper.eq("on_board_ssp", "N");
        }
        if(StringUtils.isNotEmpty(supplyRisk.getImportDomestic())){
            queryWrapper.eq("import_domestic",supplyRisk.getImportDomestic());
        }
        queryWrapper.notLike("plant", "%SSVF%");
        List<SupplyRisk> supplyRiskList = supplyRiskMapper.selectList(queryWrapper);
        return supplyRiskList;
    }
    ////////////////////////////////////////////////////////////
    //Dashboard导出
    @Override
    public List<SupplyRiskVo> exportDashboard(SupplyRisk supplyRisk) {
        return supplyRiskMapper.exportDashboard(supplyRisk);
    }

    //数据导入
    @Override
    public String importSupplyRisk(List<SupplyRiskVo> supplyRiskVoListList, boolean updateSupport) {
        if (StringUtils.isNull(supplyRiskVoListList) || supplyRiskVoListList.size() == 0) {
            throw new ServiceException("导入supplyRisk数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        //添加集合
        List<SupplyRisk> addSupplyRisk = Collections.synchronizedList(new ArrayList<>());
        List<PlantRisk> addPlantRisk = Collections.synchronizedList(new ArrayList<>());
        //修改集合
        List<SupplyRisk> updSupplyRisk = Collections.synchronizedList(new ArrayList<>());
        List<PlantRisk> updPlantRisk = Collections.synchronizedList(new ArrayList<>());
        for (SupplyRiskVo supplyRiskVo : supplyRiskVoListList) {
            try {
                //判断该信息存不存在
                SupplyRisk s = supplyRiskMapper.getInfo(supplyRiskVo.getPlant(), supplyRiskVo.getMaterialNo());
                if (StringUtils.isNull(s)) {
                    SupplyRisk supplyRisk = new SupplyRisk();
                    BeanUtils.copyProperties(supplyRiskVo, supplyRisk, SupplyRisk.class);
                    if(StringUtils.isNotEmpty(supplyRiskVo.getPlantRiskInputs())){
                        //替换空格
                        String afterValue = supplyRiskVo.getPlantRiskInputs().replaceAll("\\s+", "");
                        //判断是否包含
                        if (Pattern.compile(Pattern.quote("lowrisk"), Pattern.CASE_INSENSITIVE).matcher(afterValue).find()) {
                            supplyRisk.setRisk("L");
                        }
                        if (Pattern.compile(Pattern.quote("highrisk"), Pattern.CASE_INSENSITIVE).matcher(afterValue).find()) {
                            supplyRisk.setRisk("H");
                        }
                        if (Pattern.compile(Pattern.quote("mediumrisk"), Pattern.CASE_INSENSITIVE).matcher(afterValue).find()) {
                            supplyRisk.setRisk("M");
                        }
                    }
                    addSupplyRisk.add(supplyRisk);

                    PlantRisk plantRisk = new PlantRisk();
                    //赋值
                    plantRisk.setPlant(supplyRiskVo.getPlant());
                    plantRisk.setMaterialNo(supplyRiskVo.getMaterialNo());
                    plantRisk.setPlantRiskInputs(supplyRiskVo.getPlantRiskInputs());
                    plantRisk.setLocalizationMassProductionDate(supplyRiskVo.getLocalizationMassProductionDate());
                    QueryWrapper<PlantRisk> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("plant", plantRisk.getPlant());
                    queryWrapper.eq("material_no", plantRisk.getMaterialNo());
                    PlantRisk onePlantRisk = plantRiskMapper.selectOne(queryWrapper);
                    if (StringUtils.isNotNull(onePlantRisk)) {
                        plantRiskMapper.update(plantRisk, queryWrapper);
                    } else {
                        plantRiskMapper.insert(plantRisk);
                    }

                    successNum++;
                    successMsg.append("<br/>" + successNum + "、工厂: " + supplyRiskVo.getPlant() + "、材料号: " + supplyRiskVo.getMaterialNo() + " 导入成功");
                } else if (updateSupport) {
                    SupplyRisk supplyRisk = new SupplyRisk();
                    BeanUtils.copyProperties(supplyRiskVo, supplyRisk, SupplyRisk.class);
                    if(StringUtils.isNotEmpty(supplyRiskVo.getPlantRiskInputs())){
                        //替换空格
                        String afterValue = supplyRiskVo.getPlantRiskInputs().replaceAll("\\s+", "");
                        //判断是否包含
                        if (Pattern.compile(Pattern.quote("lowrisk"), Pattern.CASE_INSENSITIVE).matcher(afterValue).find()) {
                            supplyRisk.setRisk("L");
                        }
                        if (Pattern.compile(Pattern.quote("highrisk"), Pattern.CASE_INSENSITIVE).matcher(afterValue).find()) {
                            supplyRisk.setRisk("H");
                        }
                        if (Pattern.compile(Pattern.quote("mediumrisk"), Pattern.CASE_INSENSITIVE).matcher(afterValue).find()) {
                            supplyRisk.setRisk("M");
                        }
                    }
                    supplyRisk.setId(s.getId());
                    updSupplyRisk.add(supplyRisk);

                    PlantRisk plantRisk = new PlantRisk();
                    //赋值
                    plantRisk.setPlant(supplyRiskVo.getPlant());
                    plantRisk.setMaterialNo(supplyRiskVo.getMaterialNo());
                    plantRisk.setPlantRiskInputs(supplyRiskVo.getPlantRiskInputs());
                    plantRisk.setLocalizationMassProductionDate(supplyRiskVo.getLocalizationMassProductionDate());
                    QueryWrapper<PlantRisk> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("plant", plantRisk.getPlant());
                    queryWrapper.eq("material_no", plantRisk.getMaterialNo());
                    PlantRisk onePlantRisk = plantRiskMapper.selectOne(queryWrapper);
                    if (StringUtils.isNotNull(onePlantRisk)) {
                        plantRiskMapper.update(plantRisk, queryWrapper);
                    } else {
                        plantRiskMapper.insert(plantRisk);
                    }

                    successNum++;
                    successMsg.append("<br/>" + successNum + "、工厂: " + supplyRiskVo.getPlant() + "、材料号: " + supplyRiskVo.getMaterialNo() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + successNum + "、工厂: " + supplyRiskVo.getPlant() + "、材料号: " + supplyRiskVo.getMaterialNo() + " 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + successNum + "、工厂: " + supplyRiskVo.getPlant() + "、材料号: " + supplyRiskVo.getMaterialNo() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        //添加持久化
        if (addSupplyRisk.size() != 0) {
            super.saveBatch(addSupplyRisk);
        }
        if (addPlantRisk.size() != 0) {
            plantRiskService.saveBatch(addPlantRisk);
        }
        //修改持久化
        if (updSupplyRisk.size() != 0) {
            super.updateBatchById(updSupplyRisk);
        }
        if (updPlantRisk.size() != 0) {
            plantRiskMapper.updatePlantRisk(updPlantRisk);
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public List<String> getSupplierList() {
        return supplyRiskMapper.getSupplierList();
    }
}
