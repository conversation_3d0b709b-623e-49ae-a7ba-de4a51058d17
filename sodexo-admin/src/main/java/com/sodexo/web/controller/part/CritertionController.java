package com.sodexo.web.controller.part;

import com.fasterxml.jackson.databind.ser.Serializers;
import com.sodexo.common.core.controller.BaseController;
import com.sodexo.common.core.page.TableDataInfo;
import com.sodexo.system.domain.Critertion;
import com.sodexo.system.domain.SupplyRisk;
import com.sodexo.system.service.ICritertionService;
import com.sodexo.system.service.impl.CritertionServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-27
 */
@RestController
@RequestMapping("/part/critertion")
public class CritertionController extends BaseController {
    @Autowired
    private ICritertionService critertionService;
    //获取结果集
    @GetMapping("/list")
    public TableDataInfo list(Critertion critertion)
    {
        List<Critertion> list = critertionService.queryAll(critertion);
        return getDataTable(list);
    }
}
