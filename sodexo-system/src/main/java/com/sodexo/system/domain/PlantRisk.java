package com.sodexo.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sodexo.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@TableName("plant_risk")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PlantRisk implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * Plant
     */
    @TableField(value = "plant")
    private String plant;

    /**
     * Material No.
     */
    @TableField(value = "material_no")
    private String materialNo;

    /**
     * Localization Mass Production Date
     */
    @TableField(value = "localization_mass_production_date")
    private Date localizationMassProductionDate;

    /**
     * Plant Risk Inputs
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @TableField(value = "plant_risk_inputs")
    private String plantRiskInputs;


}
