package com.sodexo.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sodexo.common.utils.SecurityUtils;
import com.sodexo.system.domain.Browse;
import com.sodexo.system.domain.vo.BrowseVo;
import com.sodexo.system.mapper.BrowseMapper;
import com.sodexo.system.service.IBrowseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
@Service
public class BrowseServiceImpl extends ServiceImpl<BrowseMapper, Browse> implements IBrowseService {

    @Resource
    private BrowseMapper browseMapper;

    @Override
    public int add(Browse browse) {
        browse.setOperName(SecurityUtils.getUsername());
        return browseMapper.add(browse);
    }

    @Override
    public List<BrowseVo> dataChart(Browse browse) {
        return browseMapper.dataChart(browse);
    }

    @Override
    public List<BrowseVo> workData(Browse browse) {
        return browseMapper.workData(browse);
    }

    @Override
    public List<Browse> export(Browse browse) {
        return browseMapper.export(browse);
    }
}
