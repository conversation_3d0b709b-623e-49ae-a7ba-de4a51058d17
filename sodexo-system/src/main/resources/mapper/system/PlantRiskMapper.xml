<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sodexo.system.mapper.PlantRiskMapper">

    <update id="updatePlantRisk" parameterType="list">
        <foreach item="item" index="index" collection="list" separator=";">
            UPDATE plant_risk
            SET localization_mass_production_date = #{item.localizationMassProductionDate},
            plant_risk_inputs = #{item.plantRiskInputs}
            WHERE plant = #{item.plant}  and material_no = #{item.materialNo}
        </foreach>
    </update>

    <select id="getInfo" resultType="plantRisk">
        select * from  plant_risk where  plant = #{plant}  and  material_no = #{materialNo}
    </select>
</mapper>
