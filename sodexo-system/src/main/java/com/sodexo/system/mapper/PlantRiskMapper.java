package com.sodexo.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sodexo.system.domain.PlantRisk;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
public interface PlantRiskMapper extends BaseMapper<PlantRisk> {

    PlantRisk getInfo(@Param("plant") String plant,@Param("materialNo") String materialNo);

    void updatePlantRisk(@Param("list") List<PlantRisk> updPlantRisk);
}
