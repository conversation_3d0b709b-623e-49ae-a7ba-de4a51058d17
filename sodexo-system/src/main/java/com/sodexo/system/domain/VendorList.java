package com.sodexo.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sodexo.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Data
@TableName("vendor_list")
@NoArgsConstructor
@AllArgsConstructor
public class VendorList implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * Vendor Code
     */
    @TableField(value = "plant_code")
    @Excel(name = "Plant Code")
    private String plantCode;

    /**
     * Vendor Name
     */
    @TableField(value = "vendor_name")
    @Excel(name = "Vendor Name")
    private String vendorName;


    @TableField(value = "import_domestic")
    @Excel(name = "Import/Domestic")
    private String importDomestic;

    @TableField(value = "plant")
    @Excel(name = "Plant")
    private String plant;

    @TableField(value = "category")
    @Excel(name = "Category")
    private String category;

    @TableField(exist = false)
    private String pageNum;


    @TableField(exist = false)
    private String pageSize;

    @TableField(value = "on_board_ssp")
    @Excel(name = "onBoardSsp")
    private String onBoardSsp;
}
