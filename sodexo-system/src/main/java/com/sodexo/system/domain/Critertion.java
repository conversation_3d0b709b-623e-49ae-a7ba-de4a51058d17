package com.sodexo.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-27
 */
public class Critertion implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 运输
     */
    private String name;

    /**
     * H
     */
    private String h;

    /**
     * M
     */
    private String m;

    /**
     * L
     */
    private String l;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getH() {
        return h;
    }

    public void setH(String h) {
        this.h = h;
    }
    public String getM() {
        return m;
    }

    public void setM(String m) {
        this.m = m;
    }
    public String getL() {
        return l;
    }

    public void setL(String l) {
        this.l = l;
    }

    @Override
    public String toString() {
        return "Critertion{" +
            "id=" + id +
            ", name=" + name +
            ", h=" + h +
            ", m=" + m +
            ", l=" + l +
        "}";
    }
}
