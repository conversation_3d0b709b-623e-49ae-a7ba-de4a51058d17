package com.sodexo.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sodexo.system.domain.Critertion;
import com.sodexo.system.domain.SupplyRisk;
import com.sodexo.system.mapper.CritertionMapper;
import com.sodexo.system.service.ICritertionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-27
 */
@Service
public class CritertionServiceImpl extends ServiceImpl<CritertionMapper, Critertion> implements ICritertionService {

    @Resource
    private CritertionMapper critertionMapper;

    @Override
    public List<Critertion> queryAll(Critertion critertion) {
        return critertionMapper.selectList(null);
    }
}
