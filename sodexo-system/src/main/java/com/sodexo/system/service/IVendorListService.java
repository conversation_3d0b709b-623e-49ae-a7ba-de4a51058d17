package com.sodexo.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sodexo.system.domain.VendorList;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
public interface IVendorListService extends IService<VendorList> {

    List<VendorList> queryAll(VendorList vendorList);

    VendorList find(String vendorCode);

    String importData(List<VendorList> list, boolean updateSupport);

    int add(VendorList vendorList);

    int upd(VendorList vendorList);

    int del(Long[] ids);
}
