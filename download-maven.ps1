# Download and setup Maven
$mavenVersion = "3.9.6"
$mavenUrl = "https://archive.apache.org/dist/maven/maven-3/$mavenVersion/binaries/apache-maven-$mavenVersion-bin.zip"
$mavenZip = "apache-maven-$mavenVersion-bin.zip"
$mavenDir = "apache-maven-$mavenVersion"

Write-Host "Downloading Maven $mavenVersion..."
Invoke-WebRequest -Uri $mavenUrl -OutFile $mavenZip

Write-Host "Extracting Maven..."
Expand-Archive -Path $mavenZip -DestinationPath . -Force

Write-Host "Setting up Maven environment..."
$env:MAVEN_HOME = "$PWD\$mavenDir"
$env:PATH = "$env:MAVEN_HOME\bin;$env:PATH"

Write-Host "Maven setup complete. Testing..."
& "$mavenDir\bin\mvn.cmd" --version

Write-Host "Running Maven package..."
& "$mavenDir\bin\mvn.cmd" clean package -DskipTests
