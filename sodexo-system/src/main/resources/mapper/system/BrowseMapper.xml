<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sodexo.system.mapper.BrowseMapper">
    <insert id="add">
        INSERT INTO browse (title, oper_name)
        VALUES (#{title}, #{operName})
    </insert>

    <select id="dataChart" resultType="com.sodexo.system.domain.vo.BrowseVo">
        SELECT title,COUNT(*) AS num FROM browse WHERE 1=1
        <if test="operName != null and operName != ''">
          and   oper_name = #{operName}
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND oper_data &gt;= #{params.beginTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND oper_data &lt;= #{params.endTime}
        </if>
        GROUP  BY title;
    </select>
    <select id="workData" resultType="com.sodexo.system.domain.vo.BrowseVo">
       select  oper_name,COUNT(*) AS num FROM browse WHERE title = #{title}
        <if test="operName != null and operName != ''">
            and   oper_name = #{operName}
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND oper_data &gt;= #{params.beginTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND oper_data &lt;= #{params.endTime}
        </if>
         GROUP  BY oper_name;
    </select>
    <select id="export" resultType="com.sodexo.system.domain.Browse">
        SELECT * FROM browse WHERE 1=1
        <if test="operName != null and operName != ''">
            and   oper_name = #{operName}
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND oper_data &gt;= #{params.beginTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND oper_data &lt;= #{params.endTime}
        </if>
    </select>
</mapper>
