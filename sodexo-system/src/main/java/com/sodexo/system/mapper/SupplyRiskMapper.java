package com.sodexo.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sodexo.system.domain.SupplyRisk;
import com.sodexo.system.domain.vo.SupplyRiskVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
public interface SupplyRiskMapper extends BaseMapper<SupplyRisk> {

    SupplyRisk getInfo(@Param("plant") String plant,@Param("materialNo") String materialNo);

    List<SupplyRiskVo> selectSupplyRiskList(@Param("supplyRisk") SupplyRisk supplyRisk);

    SupplyRiskVo selectSupplyRiskVoById(@Param("index") Long index);

    List<SupplyRiskVo> exportDashboard(SupplyRisk supplyRisk);

    List<String> getSupplierList();
}
