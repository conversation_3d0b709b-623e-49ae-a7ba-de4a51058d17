package com.sodexo.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sodexo.system.domain.SupplyRisk;
import com.sodexo.system.domain.vo.DataChart;
import com.sodexo.system.domain.vo.SupplyRiskVo;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
public interface ISupplyRiskService extends IService<SupplyRisk> {

    List<SupplyRiskVo> selectSupplyRiskList(SupplyRisk supplyRisk);

    int upd(SupplyRiskVo supplyRiskVo);

    SupplyRiskVo selectSupplyRiskByIndex(Long index);

    List<Map<String, Object>> scale(SupplyRisk supplyRisk);

    List<SupplyRisk>dataChart(SupplyRisk supplyRisk);



    List<SupplyRisk> getMList(SupplyRisk supplyRisk);

    List<SupplyRiskVo> exportDashboard(SupplyRisk supplyRisk);

    String importSupplyRisk(List<SupplyRiskVo> supplyRiskVoListList, boolean updateSupport);

    List<String> getSupplierList();
}
