package com.sodexo.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sodexo.common.exception.ServiceException;
import com.sodexo.common.utils.StringUtils;
import com.sodexo.system.domain.PlantRisk;
import com.sodexo.system.domain.SupplyRisk;
import com.sodexo.system.domain.VendorList;
import com.sodexo.system.domain.vo.SupplyRiskVo;
import com.sodexo.system.mapper.VendorListMapper;
import com.sodexo.system.service.IVendorListService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Service
public class VendorListServiceImpl extends ServiceImpl<VendorListMapper, VendorList> implements IVendorListService {

    @Resource
    private VendorListMapper vendorListMapper;

    @Override
    public List<VendorList> queryAll(VendorList vendorList) {
        QueryWrapper<VendorList> queryWrapper=new QueryWrapper<>();
        if(StringUtils.isNotEmpty(vendorList.getPlantCode())){
            queryWrapper.like("plant_code",vendorList.getPlantCode());
        }
        if(StringUtils.isNotEmpty(vendorList.getVendorName())){
            queryWrapper.like("vendor_name",vendorList.getVendorName());

        }
        if(StringUtils.isNotEmpty(vendorList.getOnBoardSsp())){
            queryWrapper.eq("`on_board_ssp`",vendorList.getOnBoardSsp());

        }
        return vendorListMapper.selectList(queryWrapper);
    }

    @Override
    public VendorList find(String vendorCode) {
        return vendorListMapper.getInfo(vendorCode);
    }



    @Override
    public int add(VendorList vendorList) {
        return vendorListMapper.insert(vendorList);
    }

    @Override
    public int upd(VendorList vendorList) {
        return vendorListMapper.updateById(vendorList);
    }

    @Override
    public int del(Long[] ids) {
        return vendorListMapper.deleteBatchIds(Arrays.asList(ids));
    }

    @Override
    public String importData(List<VendorList> list, boolean updateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0) {
            throw new ServiceException("导入Vendor数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        //添加集合
        List<VendorList> addVendorList = Collections.synchronizedList(new ArrayList<>());
        //修改集合
        List<VendorList> updVendorList = Collections.synchronizedList(new ArrayList<>());
        for (VendorList vendorList : list) {
            try {

                //判断该信息存不存在
                VendorList s = vendorListMapper.getInfo(vendorList.getPlantCode());
                if (StringUtils.isNull(s)) {
                    addVendorList.add(vendorList);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、Plant Code: " + vendorList.getPlantCode() + " 导入成功");
                } else if (updateSupport) {
                    vendorList.setId(s.getId());
                    updVendorList.add(vendorList);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、Plant Code: " + vendorList.getPlantCode() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + successNum + "、Plant Code: " + vendorList.getPlantCode() +" 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + successNum + "、Plant Code: " + vendorList.getPlantCode() +  " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        //添加持久化
        if (addVendorList.size() != 0) {
            super.saveBatch(addVendorList);
        }
        //修改持久化
        if (updVendorList.size() != 0) {
            super.updateBatchById(updVendorList);
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
