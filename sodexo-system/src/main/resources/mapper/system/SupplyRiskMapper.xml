<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sodexo.system.mapper.SupplyRiskMapper">

    <select id="getInfo" resultType="supplyRisk">
        SELECT *
        FROM supply_risk
        WHERE plant = #{plant}
          AND material_no = #{materialNo}
    </select>

    <select id="selectSupplyRiskList" resultType="supplyRiskVo">
        SELECT * FROM supply_risk s LEFT JOIN plant_risk p ON s.plant = p.plant AND s.material_no=p.material_no
        where 1=1
        <if test="supplyRisk.plantArray != null and supplyRisk.plantArray.length>0 ">
            and s.plant in
            <foreach item="item" index="index" collection="supplyRisk.plantArray" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

<!--        <if test=" supplyRisk.productCategoryPl == 'HD'">-->
<!--            and s.product_category_pl like CONCAT('HDPNL%')-->
<!--        </if>-->
<!--        <if test="supplyRisk.productCategoryPl != null and supplyRisk.productCategoryPl != '' and supplyRisk.productCategoryPl != 'HD'">-->
<!--            and s.product_category_pl like CONCAT('%',#{supplyRisk.productCategoryPl})-->
<!--        </if>-->

        <if test="supplyRisk.productCategoryPlArray != null and supplyRisk.productCategoryPlArray.length>0">
            and
            <foreach item="item" index="index" collection="supplyRisk.productCategoryPlArray" open="(" close=")" separator=" or ">
                <if test="  item == 'HD'">
                     s.product_category_pl like CONCAT('HDPNL%')
                </if>
                <if test=" item != 'HD'">
                     s.product_category_pl like CONCAT('%',#{item})
                </if>
            </foreach>
        </if>

        <if test="supplyRisk.shippingModeArray != null and supplyRisk.shippingModeArray.length>0">
            and s.shipping_mode in
            <foreach item="item" index="index" collection="supplyRisk.shippingModeArray" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplyRisk.riskArray != null and supplyRisk.riskArray.length>0">
            and s.risk in
            <foreach item="item" index="index" collection="supplyRisk.riskArray" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplyRisk.supplierArray != null and supplyRisk.supplierArray.length>0">
            and s.tier_1_vendor_name in
            <foreach item="item" index="index" collection="supplyRisk.supplierArray" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplyRisk.stockingPolicy != null and supplyRisk.stockingPolicy != ''">
            and s.stocking_policy = #{supplyRisk.stockingPolicy}
        </if>
        <if test="supplyRisk.materialNo != null and supplyRisk.materialNo != ''">
            and s.material_no like CONCAT('%',#{supplyRisk.materialNo},'%')
        </if>
        <if test="supplyRisk.stockingPolicyArray != null and supplyRisk.stockingPolicyArray.length>0">
            and s.stocking_policy in
            <foreach item="item" index="index" collection="supplyRisk.stockingPolicyArray" open="(" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        <if test="supplyRisk.tier1VendorCode != null and supplyRisk.tier1VendorCode != ''">
            and s.tier_1_vendor_code = #{supplyRisk.tier1VendorCode}
        </if>

        <if test="supplyRisk.containSSVFPlant != null and supplyRisk.containSSVFPlant !='' and supplyRisk.containSSVFPlant == 'no'">
            and s.plant not like '%SSVF%'
        </if>
        <if test="supplyRisk.containSSVFPlant != null and supplyRisk.containSSVFPlant !='' and supplyRisk.containSSVFPlant == 'yes'">
            and s.plant like '%SSVF%'
        </if>
        <if test="supplyRisk.plantRiskInputs == 'YES'">
            and p.plant_risk_inputs is not null AND p.plant_risk_inputs != ' '
        </if>
        <if test="supplyRisk.plantRiskInputs == 'NO'">
            and (p.plant_risk_inputs is null OR p.plant_risk_inputs = '')
        </if>
        <if test="supplyRisk.originalFactory == 'YES'">
            and s.original_factory is not null AND s.original_factory != ' '
        </if>
        <if test="supplyRisk.originalFactory == 'NO'">
            and (s.original_factory is null OR s.original_factory = '')
        </if>
        <if test="supplyRisk.importDomestic != null and supplyRisk.importDomestic != ''">
            and s.import_domestic = #{supplyRisk.importDomestic}
        </if>
        <if test="supplyRisk.onBoardSsp != null and supplyRisk.onBoardSsp != ''">
            and s.on_board_ssp = #{supplyRisk.onBoardSsp}
        </if>
    </select>

    <select id="selectSupplyRiskVoById" resultType="supplyRiskVo">
        SELECT *
        FROM supply_risk s
                 LEFT JOIN plant_risk p ON s.plant = p.plant AND s.material_no = p.material_no
        where s.id = #{index}
    </select>

    <select id="exportDashboard" resultType="supplyRiskVo">
        SELECT * FROM supply_risk s LEFT JOIN plant_risk p ON s.plant = p.plant AND s.material_no=p.material_no
        where 1=1
        <if test="plantArray!=null and plantArray.length > 0">
           and s.plant in
            <foreach item="item" index="index" collection="plantArray" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="productCategoryPlArray != null and productCategoryPlArray.length > 0">
            AND s.product_category_pl IN
            <foreach item="item" index="index" collection="productCategoryPlArray" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="riskArray != null and riskArray.length > 0">
            AND s.risk IN
            <foreach item="item" index="index" collection="riskArray" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="materialNo != null and materialNo != ''">
            AND s.material_no = #{materialNo}
        </if>
        <if test="stockingPolicy != null and stockingPolicy.equals('true')">
            AND s.stocking_policy = 'MTS'
        </if>
        AND s.plant NOT LIKE CONCAT('%SSVF%')

    </select>
    <select id="getSupplierList" resultType="java.lang.String">
        SELECT tier_1_vendor_name FROM `supply_risk`  group by tier_1_vendor_name
    </select>

</mapper>
