package com.sodexo.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sodexo.system.domain.Browse;
import com.sodexo.system.domain.vo.BrowseVo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
public interface IBrowseService extends IService<Browse> {

    int add(Browse browse);

    List<BrowseVo> dataChart(Browse browse);

    List<BrowseVo>  workData(Browse browse);

    List<Browse> export(Browse browse);
}
