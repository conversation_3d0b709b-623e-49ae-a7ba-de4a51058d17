package com.sodexo.web.controller.part;

import com.sodexo.common.annotation.Log;
import com.sodexo.common.constant.CacheConstants;
import com.sodexo.common.core.controller.BaseController;
import com.sodexo.common.core.domain.AjaxResult;
import com.sodexo.common.core.domain.entity.SysUser;
import com.sodexo.common.core.page.TableDataInfo;
import com.sodexo.common.enums.BusinessType;
import com.sodexo.common.utils.poi.ExcelUtil;
import com.sodexo.system.domain.SupplyRisk;
import com.sodexo.system.domain.VendorList;
import com.sodexo.system.domain.vo.SupplyRiskVo;
import com.sodexo.system.service.IVendorListService;
import com.sodexo.system.service.impl.VendorListServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@RestController
@RequestMapping("/part/vendorList")
public class VendorListController extends BaseController {

    private static  final String KEY="vendor_list";

    @Autowired
    private IVendorListService vendorListService;

    @GetMapping("/select")
    public TableDataInfo select(VendorList vendorList)
    {
        List<VendorList> list = vendorListService.queryAll(vendorList);
        return getDataTable(list);
    }

    @GetMapping("/list")
    @Cacheable(value = CacheConstants.SUPPLY_RISK+KEY)
    public TableDataInfo list(VendorList vendorList)
    {
        startPage();
        List<VendorList> list = vendorListService.queryAll(vendorList);
        return getDataTable(list);
    }

    //查看详情
    @GetMapping(value = "/{vendorCode}")
    public AjaxResult getInfo(@PathVariable String vendorCode)
    {
        return success(vendorListService.find(vendorCode));
    }

    //添加
    @PostMapping
    @PreAuthorize("@ss.hasPermi('part:vendor:add')")
    @Log(title = "Vendor添加",businessType = BusinessType.INSERT)
    @CacheEvict(value = CacheConstants.SUPPLY_RISK+KEY,allEntries = true)
    public AjaxResult add(@RequestBody @Validated VendorList vendorList){
        return  toAjax(vendorListService.add(vendorList));
    }
    //修改
    @PutMapping
    @PreAuthorize("@ss.hasPermi('part:vendor:edit')")
    @Log(title = "Vendor修改",businessType=BusinessType.UPDATE)
    @CacheEvict(value = CacheConstants.SUPPLY_RISK+KEY,allEntries = true)
    public AjaxResult update(@RequestBody @Validated VendorList vendorList){
        return toAjax(vendorListService.upd(vendorList));
    }

    //删除
    @PreAuthorize("@ss.hasPermi('part:vendor:remove')")
    @Log(title = "Vendor删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @CacheEvict(value = CacheConstants.SUPPLY_RISK+KEY,allEntries = true)
    public AjaxResult remove(@PathVariable Long[] ids){
        return toAjax(vendorListService.del(ids));
    }

    //导出
    @Log(title = "Vendor导出", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermi('part:vendor:export')")
    public void exportData(HttpServletResponse response, VendorList vendorList)
    {
        List<VendorList> list = vendorListService.queryAll(vendorList);
        ExcelUtil<VendorList> util = new ExcelUtil<VendorList>(VendorList.class);
        util.exportExcel(response, list, "VendorList");
    }
    //下载模板
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<VendorList> util = new ExcelUtil<VendorList>(VendorList.class);
        util.importTemplateExcel(response,  "VendorList");
    }

    //导入
    @Log(title = "Vendor导入", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('part:vendor:import')")
    @PostMapping("/importData")
    @CacheEvict(value = CacheConstants.SUPPLY_RISK+KEY,allEntries = true)
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<VendorList> util = new ExcelUtil<VendorList>(VendorList.class);
        List<VendorList>  list = util.importExcel(file.getInputStream());
        String message = vendorListService.importData(list, updateSupport);
        return success(message);
    }
}
