package com.sodexo.web.controller.system;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/system")
public class SystemRestart {
    @PostMapping(value = "/restart")
    @PreAuthorize("@ss.hasPermi('system:restart')")
    public void restartSystem(){
        System.exit(0);

    }
}
