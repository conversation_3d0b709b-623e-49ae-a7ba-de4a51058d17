package com.sodexo.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sodexo.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *     实体类
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@TableName("supply_risk")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SupplyRisk implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;

    /**
     * 下标
     */
    @Excel(name = "Index")
    @TableField(value = "`index`")
    private Integer index;

    /**
     * Date
     */
    @Excel(name = "Date",dateFormat = "yyyy-MM-dd")
    @TableField("`date`")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * Plant
     */
    @Excel(name = "Plant")
    @TableField("`plant`")
    private String plant;

    /**
     * Material No
     */
    @Excel(name = "Material No")
    @TableField("`material_no`")
    private String materialNo;

    /**
     * Trim
     */
    @Excel(name = "Trim")
    @TableField("`trim`")
    private String trim;

    /**
     * Description
     */
    @Excel(name = "Description")
    @TableField("`description`")
    private String description;

    /**
     * Product category-PL
     */
    @Excel(name = "Product category-PL")
    @TableField("`product_category_pl`")
    private String productCategoryPl;

    /**
     * Product category-PF
     */
    @Excel(name = "Product category-PF")
    @TableField("`product_category_pf`")
    private String productCategoryPf;

    /**
     * Unit Price(RMB/PCS)
     */
    @Excel(name = "Unit Price(RMB/PCS)")
    @TableField("`unit_price`")
    private Double unitPrice;

    /**
     * LT
     */
    @Excel(name = "LeadTime")
    @TableField(value = "`lead_time`")
    private Double leadTime;

    /**
     * Shipping mode
     */
    @Excel(name = "Shipping mode")
    @TableField("`shipping_mode`")
    private String shippingMode;

    /**
     * Tier 1 Vendor code
     */
    @Excel(name = "Tier 1 Vendor code")
    @TableField("`tier_1_vendor_code`")
    private String tier1VendorCode;

    /**
     * Tier 1 Vendor Name 
     */
    @Excel(name = "Tier 1 Vendor Name")
    @TableField("`tier_1_vendor_name`")
    private String tier1VendorName;

    /**
     * Original Factory
     */
    @Excel(name = "Original Factory")
    @TableField("`original_factory`")
    private String originalFactory;

    /**
     * Safety stock(PCS)
     */
    @Excel(name = "Safety stock(PCS)")
    @TableField("`safety_stock`")
    private Double safetyStock;

    /**
     * STOCKING_POLICY
     */
    @Excel(name = "STOCKING_POLICY")
    @TableField("`stocking_policy`")
    private String stockingPolicy;

    /**
     * MOQ(PCS)
     */
    @Excel(name = "MOQ(PCS)")
    @TableField("`moq`")
    private Double moq;

    /**
     * AMU(PCS/Month)
     */
    @Excel(name = "AMU(PCS/Month)")
    @TableField("`amu`")
    private Double amu;

    /**
     * AMF(PCS/Month)
     */
    @Excel(name = "AMF(PCS/Month)")
    @TableField("`amf`")
    private Double amf;

    /**
     * SOH+QI(PCS)
     */
    @Excel(name = "SOH+QI(PCS)")
    @TableField("`soh_qi`")
    private Double sohQi;

    /**
     * Intransit(PCS)
     */

    @Excel(name = "Intransit(PCS)")
    @TableField("`intransit`")
    private Double intransit;

    /**
     * Open PO(pcs)
     */
    @Excel(name = "Open PO(pcs)")
    @TableField("`open_po`")
    private Double openPo;

    /**
     * Safety stock Coverage WKD(AMU AMF Max)
     */
    @Excel(name = "Safety stock Coverage WKD(AMU AMF Max)")
    @TableField("`safety_stock_coverage_wkd`")
    private Double safetyStockCoverageWkd;

    /**
     * SOH+Intransit Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "SOH+Intransit Coverage WKD(AMU AMF MAX)")
    @TableField("`soh_intransit_coverage_wkd`")
    private Double sohIntransitCoverageWkd;

    /**
     * SOH+Open PO Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "SOH+Open PO Coverage WKD(AMU AMF MAX)")
    @TableField("`soh_open_po_coverage_wkd`")
    private Double sohOpenPoCoverageWkd;

    /**
     * Risk 
     */
    @Excel(name = "Risk")
    @TableField("`risk`")
    private String risk;

    /**
     * RCA
     */
    @Excel(name = "RCA")
    @TableField("`rca`")
    private String rca;

    /**
     * Localization plan(Y/N)
     */
    @Excel(name = "Localization plan(Y/N)")
    @TableField("`localization_plan`")
    private String localizationPlan;

    /**
     * WK-1 SOH+QI(PCS)
     */
    @Excel(name = "WK-1 SOH+QI(PCS)")
    @TableField("`wk_1_soh_qi`")
    private Double wk1SohQi;

    /**
     * WK-1 Intransit(PCS)
     */
    @Excel(name = "WK-1 Intransit(PCS)")
    @TableField("`wk_1_intransit`")
    private Double wk1Intransit;

    /**
     * WK-1 Open PO(pcs)
     */
    @Excel(name = "WK-1 Open PO(pcs)")
    @TableField("`wk_1_open_po`")
    private Double wk1OpenPo;

    /**
     * WK-1 SOH+Intransit Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-1 SOH+Intransit Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_1_soh_intransit_coverage_wkd`")
    private Double wk1SohIntransitCoverageWkd;

    /**
     * WK-1 SOH+Open PO Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-1 SOH+Open PO Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_1_soh_open_po_coverage_wkd`")
    private Double wk1SohOpenPoCoverageWkd;

    /**
     * WK-1 Progress Vs last week
     */
    @Excel(name = "WK-1 Progress Vs last week")
    @TableField("`wk_1_progress_vs_last_week`")
    private Double wk1ProgressVsLastWeek;

    /**
     * WK-2 SOH+QI(PCS)
     */
    @Excel(name = "WK-2 SOH+QI(PCS)")
    @TableField("`wk_2_soh_qi`")
    private Double wk2SohQi;

    /**
     * WK-2 Intransit(PCS)
     */
    @Excel(name = "WK-2 Intransit(PCS)")
    @TableField("`wk_2_intransit`")
    private Double wk2Intransit;

    /**
     * WK-2 Open PO(pcs)
     */
    @Excel(name = "WK-2 Open PO(pcs)")
    @TableField("`wk_2_open_po`")
    private Double wk2OpenPo;

    /**
     * WK-2 SOH+Intransit Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-2 SOH+Intransit Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_2_soh_intransit_coverage_wkd`")
    private Double wk2SohIntransitCoverageWkd;

    /**
     * WK-2 SOH+Open PO Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-2 SOH+Open PO Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_2_soh_open_po_coverage_wkd`")
    private Double wk2SohOpenPoCoverageWkd;

    /**
     * WK-2 Progress Vs last week
     */
    @Excel(name = "WK-2 Progress Vs last week")
    @TableField("`wk_2_progress_vs_last_week`")
    private Double wk2ProgressVsLastWeek;

    /**
     * WK-3 SOH+QI(PCS)
     */
    @Excel(name = "WK-3 SOH+QI(PCS)")
    @TableField("`wk_3_soh_qi`")
    private Double wk3SohQi;

    /**
     * WK-3 Intransit(PCS)
     */
    @Excel(name = "WK-3 Intransit(PCS)")
    @TableField("`wk_3_intransit`")
    private Double wk3Intransit;

    /**
     * WK-3 Open PO(pcs)
     */
    @Excel(name = "WK-3 Open PO(pcs)")
    @TableField("`wk_3_open_po`")
    private Double wk3OpenPo;

    /**
     * WK-3 SOH+Intransit Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-3 SOH+Intransit Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_3_soh_intransit_coverage_wkd`")
    private Double wk3SohIntransitCoverageWkd;

    /**
     * WK-3 SOH+Open PO Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-3 SOH+Open PO Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_3_soh_open_po_coverage_wkd`")
    private Double wk3SohOpenPoCoverageWkd;

    /**
     * WK-3 Progress Vs last week
     */
    @Excel(name = "WK-3 Progress Vs last week")
    @TableField("`wk_3_progress_vs_last_week`")
    private Double wk3ProgressVsLastWeek;

    /**
     * WK-4 SOH+QI(PCS)
     */
    @Excel(name = "WK-4 SOH+QI(PCS)")
    @TableField("`wk_4_soh_qi`")
    private Double wk4SohQi;

    /**
     * WK-4 Intransit(PCS)
     */
    @Excel(name = "WK-4 Intransit(PCS)")
    @TableField("`wk_4_intransit`")
    private Double wk4Intransit;

    /**
     * WK-4 Open PO(pcs)
     */
    @Excel(name = "WK-4 Open PO(pcs)")
    @TableField("`wk_4_open_po`")
    private Double wk4OpenPo;

    /**
     * WK-4 SOH+Intransit Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-4 SOH+Intransit Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_4_soh_intransit_coverage_wkd`")
    private Double wk4SohIntransitCoverageWkd;

    /**
     * WK-4 SOH+Open PO Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-4 SOH+Open PO Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_4_soh_open_po_coverage_wkd`")
    private Double wk4SohOpenPoCoverageWkd;

    /**
     * WK-4 Progress Vs last week
     */
    @Excel(name = "WK-4 Progress Vs last week")
    @TableField("`wk_4_progress_vs_last_week`")
    private Double wk4ProgressVsLastWeek;

    /**
     * WK-5 SOH+QI(PCS)
     */
    @Excel(name = "WK-5 SOH+QI(PCS)")
    @TableField("`wk_5_soh_qi`")
    private Double wk5SohQi;

    /**
     * WK-5 Intransit(PCS)
     */
    @Excel(name = "WK-5 Intransit(PCS)")
    @TableField("`wk_5_intransit`")
    private Double wk5Intransit;

    /**
     * WK-5 Open PO(pcs)
     */
    @Excel(name = "WK-5 Open PO(pcs)")
    @TableField("`wk_5_open_po`")
    private Double wk5OpenPo;

    /**
     * WK-5 SOH+Intransit Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-5 SOH+Intransit Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_5_soh_intransit_coverage_wkd`")
    private Double wk5SohIntransitCoverageWkd;

    /**
     * WK-5 SOH+Open PO Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-5 SOH+Open PO Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_5_soh_open_po_coverage_wkd`")
    private Double wk5SohOpenPoCoverageWkd;

    /**
     * WK-5 Progress Vs last week
     */
    @Excel(name = "WK-5 Progress Vs last week")
    @TableField("`wk_5_progress_vs_last_week`")
    private Double wk5ProgressVsLastWeek;

    /**
     * WK-6 SOH+QI(PCS)
     */
    @Excel(name = "WK-6 SOH+QI(PCS)")
    @TableField("`wk_6_soh_qi`")
    private Double wk6SohQi;

    /**
     * WK-6 Intransit(PCS)
     */
    @Excel(name = "WK-6 Intransit(PCS)")
    @TableField("`wk_6_intransit`")
    private Double wk6Intransit;

    /**
     * WK-6 Open PO(pcs)
     */
    @Excel(name = "WK-6 Open PO(pcs)")
    @TableField("`wk_6_open_po`")
    private Double wk6OpenPo;

    /**
     * WK-6 SOH+Intransit Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-6 SOH+Intransit Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_6_soh_intransit_coverage_wkd`")
    private Double wk6SohIntransitCoverageWkd;

    /**
     * WK-6 SOH+Open PO Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-6 SOH+Open PO Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_6_soh_open_po_coverage_wkd`")
    private Double wk6SohOpenPoCoverageWkd;

    /**
     * WK-6 Progress Vs last week
     */
    @Excel(name = "WK-6 Progress Vs last week")
    @TableField("`wk_6_progress_vs_last_week`")
    private Double wk6ProgressVsLastWeek;

    /**
     * WK-7 SOH+QI(PCS)
     */
    @Excel(name = "WK-7 SOH+QI(PCS)")
    @TableField("`wk_7_soh_qi`")
    private Double wk7SohQi;

    /**
     * WK-7 Intransit(PCS)
     */
    @Excel(name = "WK-7 Intransit(PCS)")
    @TableField("`wk_7_intransit`")
    private Double wk7Intransit;

    /**
     * WK-7 Open PO(pcs)
     */
    @Excel(name = "WK-7 Open PO(pcs)")
    @TableField("`wk_7_open_po`")
    private Double wk7OpenPo;

    /**
     * WK-7 SOH+Intransit Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-7 SOH+Intransit Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_7_soh_intransit_coverage_wkd`")
    private Double wk7SohIntransitCoverageWkd;

    /**
     * WK-7 SOH+Open PO Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-7 SOH+Open PO Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_7_soh_open_po_coverage_wkd`")
    private Double wk7SohOpenPoCoverageWkd;

    /**
     * WK-7 Progress Vs last week
     */
    @Excel(name = "WK-7 Progress Vs last week")
    @TableField("`wk_7_progress_vs_last_week`")
    private Double wk7ProgressVsLastWeek;

    /**
     * WK-8 SOH+QI(PCS)
     */
    @Excel(name = "WK-8 SOH+QI(PCS)")
    @TableField("`wk_8_soh_qi`")
    private Double wk8SohQi;

    /**
     * WK-8 Intransit(PCS)
     */
    @Excel(name = "WK-8 Intransit(PCS)")
    @TableField("`wk_8_intransit`")
    private Double wk8Intransit;

    /**
     * WK-8 Open PO(pcs)
     */
    @Excel(name = "WK-8 Open PO(pcs)")
    @TableField("`wk_8_open_po`")
    private Double wk8OpenPo;

    /**
     * WK-8 SOH+Intransit Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-8 SOH+Intransit Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_8_soh_intransit_coverage_wkd`")
    private Double wk8SohIntransitCoverageWkd;

    /**
     * WK-8 SOH+Open PO Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-8 SOH+Open PO Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_8_soh_open_po_coverage_wkd`")
    private Double wk8SohOpenPoCoverageWkd;

    /**
     * WK-8 Progress Vs last week
     */
    @Excel(name = "WK-8 Progress Vs last week")
    @TableField("`wk_8_progress_vs_last_week`")
    private Double wk8ProgressVsLastWeek;

    /**
     * WK-9 SOH+QI(PCS)
     */
    @Excel(name = "WK-9 SOH+QI(PCS)")
    @TableField("`wk_9_soh_qi`")
    private Double wk9SohQi;

    /**
     * WK-9 Intransit(PCS)
     */
    @Excel(name = "WK-9 Intransit(PCS)")
    @TableField("`wk_9_intransit`")
    private Double wk9Intransit;

    /**
     * WK-9 Open PO(pcs)
     */
    @Excel(name = "WK-9 Open PO(pcs)")
    @TableField("`wk_9_open_po`")
    private Double wk9OpenPo;

    /**
     * WK-9 SOH+Intransit Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-9 SOH+Intransit Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_9_soh_intransit_coverage_wkd`")
    private Double wk9SohIntransitCoverageWkd;

    /**
     * WK-9 SOH+Open PO Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-9 SOH+Open PO Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_9_soh_open_po_coverage_wkd`")
    private Double wk9SohOpenPoCoverageWkd;

    /**
     * WK-9 Progress Vs last week
     */
    @Excel(name = "WK-9 Progress Vs last week")
    @TableField("`wk_9_progress_vs_last_week`")
    private Double wk9ProgressVsLastWeek;

    /**
     * WK-10 SOH+QI(PCS)
     */
    @Excel(name = "WK-10 SOH+QI(PCS)")
    @TableField("`wk_10_soh_qi`")
    private Double wk10SohQi;

    /**
     * WK-10 Intransit(PCS)
     */
    @Excel(name = "WK-10 Intransit(PCS)")
    @TableField("`wk_10_intransit`")
    private Double wk10Intransit;

    /**
     * WK-10 Open PO(pcs)
     */
    @Excel(name = "WK-10 Open PO(pcs)")
    @TableField("`wk_10_open_po`")
    private Double wk10OpenPo;

    /**
     * WK-10 SOH+Intransit Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-10 SOH+Intransit Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_10_soh_intransit_coverage_wkd`")
    private Double wk10SohIntransitCoverageWkd;

    /**
     * WK-10 SOH+Open PO Coverage WKD(AMU AMF MAX)
     */
    @Excel(name = "WK-10 SOH+Open PO Coverage WKD(AMU AMF MAX)")
    @TableField("`wk_10_soh_open_po_coverage_wkd`")
    private Double wk10SohOpenPoCoverageWkd;

    /**
     * WK-10 Progress Vs last week
     */
    @Excel(name = "WK-10 Progress Vs last week")
    @TableField("`wk_10_progress_vs_last_week`")
    private Double wk10ProgressVsLastWeek;

    @TableField(exist = false)
    private String[] shippingModeArray;



    @TableField(exist = false)
    private String[] riskArray;

    @TableField(exist = false)
    private String[] plantArray;

    @TableField(exist = false)
    private String[] productCategoryPlArray;

    @TableField(exist = false)
    private String[] stockingPolicyArray;

    @TableField(exist = false)
    private String[] supplierArray;

    @TableField(exist = false)
    private String containSSVFPlant;

    @TableField(exist = false)
    private Boolean menu;

    @TableField(exist = false)
    private String plantRiskInputs;

    @Version
    @TableField("`version`")
    private Integer version;

    @TableField(value = "import_domestic")
    @Excel(name = "Import/Domestic")
    private String importDomestic;

    @Excel(name = "SupplierStock")
    @TableField("`supplier_stock`")
    private Double supplierStock;

    @Excel(name = "SupplierStockCoverage")
    @TableField("`supplier_stock_coverage_wkd`")
    private Double supplierStockCoverageWkd;

    @Excel(name = "supplierStockUpdate")
    @TableField("`supplier_stock_update`")
    private Date supplierStockUpdate;

    @Excel(name = "sohSupplierCoverageWkd")
    @TableField("`soh_supplier_coverage_wkd`")
    private Double sohSupplierCoverageWkd;

    @TableField(exist = false)
    private String pageNum;


    @TableField(exist = false)
    private String pageSize;

    @TableField(value = "on_board_ssp")
    @Excel(name = "onBoardSsp")
    private String onBoardSsp;
}
