package com.sodexo.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sodexo.system.domain.Browse;
import com.sodexo.system.domain.vo.BrowseVo;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
public interface BrowseMapper extends BaseMapper<Browse> {

    List<BrowseVo> dataChart(Browse browse);

    List<BrowseVo> workData(Browse browse);

    List<Browse> export(Browse browse);

    int add(Browse browse);
}
